"""Database connection and session management for Crawl Worker."""

import logging
from typing import Async<PERSON>enerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.pool import NullPool
from .config import settings
from .models import Base

logger = logging.getLogger(__name__)

# Create async engine
engine = create_async_engine(
    settings.database_url,
    echo=settings.debug,
    poolclass=NullPool if settings.debug else None,
    pool_pre_ping=True,
    pool_recycle=300,
)

# Create async session factory
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autocommit=False,
    autoflush=False,
)


def get_db_session():
    """Get database session for worker tasks."""
    return AsyncSessionLocal()


async def close_db():
    """Close database connections."""
    await engine.dispose()
    logger.info("Database connections closed")
